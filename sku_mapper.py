"""
SKU Mapping Tool for A3 Data and Nielsen Data
============================================

This tool maps SKUs between A3 promotional data and Nielsen retail measurement data
using EAN codes and composite SKU matching algorithms.

Author: Augment Agent
Date: 2025-06-16
"""

import pandas as pd
import re
from typing import Dict, List
from pathlib import Path
import logging

# Configure logging for better debugging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SkuMapper:
    """
    Main class for mapping SKUs between A3 and Nielsen datasets.
    
    This class handles:
    - Data loading and preprocessing
    - EAN-based exact matching
    - Composite SKU matching using normalized attributes
    - Output generation with mapping logic tracking
    """
    
    def __init__(self, a3_file_path: str, nielsen_file_path: str):
        """
        Initialize the SKU mapper with file paths.
        
        Args:
            a3_file_path (str): Path to A3 Excel file
            nielsen_file_path (str): Path to Nielsen Excel file
        """
        self.a3_file_path = Path(a3_file_path)
        self.nielsen_file_path = Path(nielsen_file_path)
        self.a3_data = None
        self.nielsen_data_by_year = {}
        self.mapping_results = {}
        
        # Normalization mappings for consistent matching
        self.manufacturer_mapping = {
            'AB INBEV FRANCE': 'AB-INBEV',
            'CARLSBERG GROUP': 'CARLSBERG',
            'HEINEKEN ENTREPRISE': 'HEINEKEN',
            'BRASSERIE GOUDALE': 'GOUDALE',
            'I.B.B.': 'IBB'
        }
        
        self.brand_mapping = {
            'ABBAYE DE LEFFE': 'LEFFE',
            'LA GOUDALE': 'GOUDALE',
            'BRASSERIE DU PELICAN': 'PELICAN',
            'SECRET DES MOINES': 'SECRET DES MOINES'
        }
        
        # Volume conversion mapping (CL to ML)
        self.volume_pattern = re.compile(r'(\d+)\s*(CL|ML)', re.IGNORECASE)
        
    def load_data(self) -> None:
        """
        Load data from both Excel files.
        
        Loads A3 data from main sheet and Nielsen data from all year sheets.
        """
        logger.info("Loading A3 data...")
        try:
            self.a3_data = pd.read_excel(
                self.a3_file_path, 
                sheet_name='a3_distribution_march_2025'
            )
            logger.info(f"Loaded {len(self.a3_data)} rows from A3 data")
        except Exception as e:
            logger.error(f"Error loading A3 data: {e}")
            raise
            
        logger.info("Loading Nielsen data...")
        try:
            # First, try to detect available sheets dynamically
            excel_file = pd.ExcelFile(self.nielsen_file_path)
            available_sheets = excel_file.sheet_names

            # Define possible sheet naming patterns
            nielsen_sheets = {}

            # Pattern 1: Year-Table-1 (e.g., "2022-Table-1")
            for sheet in available_sheets:
                if '-Table-1' in sheet:
                    year = sheet.split('-')[0]
                    if year.isdigit():
                        nielsen_sheets[year] = sheet

            # Pattern 2: Number-Table-1 (e.g., "1-Table-1" for 2022)
            if not nielsen_sheets:
                sheet_mapping = {
                    '2022': '1-Table-1',
                    '2023': '2-Table-1',
                    '2024': '3-Table-1',
                    '2024_2': '4-Table-1',  # Second 2024 dataset
                    '2025': '5-Table-1'
                }
                for year, sheet_name in sheet_mapping.items():
                    if sheet_name in available_sheets:
                        nielsen_sheets[year] = sheet_name
            
            for year, sheet_name in nielsen_sheets.items():
                df = pd.read_excel(self.nielsen_file_path, sheet_name=sheet_name, header=8)  # Header is in row 9 (index 8)

                # Clean column names (remove any extra spaces and handle NaN)
                df.columns = [str(col).strip() if pd.notna(col) else f'Col_{i}' for i, col in enumerate(df.columns)]

                # Filter out empty rows and total rows
                # Check if required columns exist
                required_cols = ['FABRICANT', 'MARQUE', 'UPC']
                missing_cols = [col for col in required_cols if col not in df.columns]
                if missing_cols:
                    logger.warning(f"Missing columns in {year}: {missing_cols}")
                    logger.info(f"Available columns: {list(df.columns)}")
                    continue

                # Remove rows where FABRICANT is empty or contains header text
                df = df.dropna(subset=required_cols)
                df = df[
                    df['FABRICANT'].notna() &
                    (df['FABRICANT'] != '') &
                    (df['FABRICANT'] != 'FABRICANT') &
                    (df['FABRICANT'] != 'Markets')
                ]
                
                self.nielsen_data_by_year[year] = df
                logger.info(f"Loaded {len(df)} rows from Nielsen {year} data")
                
        except Exception as e:
            logger.error(f"Error loading Nielsen data: {e}")
            raise
    
    def normalize_manufacturer(self, manufacturer: str) -> str:
        """
        Normalize manufacturer names for consistent matching.
        
        Args:
            manufacturer (str): Original manufacturer name
            
        Returns:
            str: Normalized manufacturer name
        """
        if pd.isna(manufacturer):
            return ''
        
        manufacturer = str(manufacturer).strip().upper()
        return self.manufacturer_mapping.get(manufacturer, manufacturer)
    
    def normalize_brand(self, brand: str) -> str:
        """
        Normalize brand names for consistent matching.
        
        Args:
            brand (str): Original brand name
            
        Returns:
            str: Normalized brand name
        """
        if pd.isna(brand):
            return ''
            
        brand = str(brand).strip().upper()
        return self.brand_mapping.get(brand, brand)
    
    def normalize_volume(self, volume_text: str) -> str:
        """
        Normalize volume measurements (convert CL to ML).
        
        Args:
            volume_text (str): Text containing volume information
            
        Returns:
            str: Normalized volume in ML
        """
        if pd.isna(volume_text):
            return ''
            
        volume_text = str(volume_text).upper()
        
        # Find all volume matches
        matches = self.volume_pattern.findall(volume_text)
        normalized_volumes = []
        
        for amount, unit in matches:
            if unit == 'CL':
                # Convert CL to ML
                ml_amount = int(amount) * 10
                normalized_volumes.append(f"{ml_amount}ML")
            else:
                normalized_volumes.append(f"{amount}ML")
        
        return ' '.join(normalized_volumes) if normalized_volumes else volume_text

    def extract_eans_from_a3(self, ean_string: str) -> List[str]:
        """
        Extract individual EAN codes from A3 EAN field (underscore separated).

        Args:
            ean_string (str): EAN string from A3 data (may contain multiple EANs)

        Returns:
            List[str]: List of individual EAN codes
        """
        if pd.isna(ean_string):
            return []

        ean_string = str(ean_string).strip()
        if not ean_string:
            return []

        # Split by underscore and clean each EAN
        eans = [ean.strip() for ean in ean_string.split('_') if ean.strip()]

        # Remove any non-numeric characters and validate length
        clean_eans = []
        for ean in eans:
            # Keep only digits
            clean_ean = re.sub(r'[^\d]', '', ean)
            if len(clean_ean) >= 8:  # Minimum EAN length
                clean_eans.append(clean_ean)

        return clean_eans

    def extract_pack_components(self, pack_info: str, data_source: str) -> dict:
        """
        Extract pack components (count, container, volume) from pack information.

        Args:
            pack_info (str): Pack information string
            data_source (str): 'a3' or 'nielsen' to determine parsing logic

        Returns:
            dict: Dictionary with 'count', 'container', 'volume' keys
        """
        if pd.isna(pack_info):
            return {'count': '', 'container': '', 'volume': ''}

        pack_info = str(pack_info).upper().strip()

        if data_source == 'a3':
            # A3 format: "12 BTL 25 CL" or "15 BOUTEILLE 25 CL"
            # Extract count (number at start), container type, and volume
            import re

            # Pattern to match: number + container + volume
            pattern = r'(\d+)\s+([A-Z]+)\s+(\d+)\s*(CL|ML)'
            match = re.search(pattern, pack_info)

            if match:
                count = match.group(1)
                container = match.group(2)
                volume_amount = match.group(3)
                volume_unit = match.group(4)

                # Normalize container names
                container_mapping = {
                    'BTL': 'BOUTEILLE',
                    'BOTTLE': 'BOUTEILLE',
                    'CAN': 'CANETTE',
                    'CANETTE': 'CANETTE',
                    'BOITE': 'BOITE',
                    'BOX': 'BOITE'
                }
                container = container_mapping.get(container, container)

                # Convert volume to ML
                if volume_unit == 'CL':
                    volume = f"{int(volume_amount) * 10}ML"
                else:
                    volume = f"{volume_amount}ML"

                return {'count': count, 'container': container, 'volume': volume}
            else:
                # Fallback: try to extract volume at least
                volume_pattern = r'(\d+)\s*(CL|ML)'
                volume_match = re.search(volume_pattern, pack_info)
                if volume_match:
                    volume_amount = volume_match.group(1)
                    volume_unit = volume_match.group(2)
                    if volume_unit == 'CL':
                        volume = f"{int(volume_amount) * 10}ML"
                    else:
                        volume = f"{volume_amount}ML"
                    return {'count': '', 'container': '', 'volume': volume}

        elif data_source == 'nielsen':
            # Nielsen format is already parsed in create_composite_sku_key
            # This is just for volume normalization
            volume_pattern = r'(\d+)\s*(CL|ML)'
            volume_match = re.search(volume_pattern, pack_info)
            if volume_match:
                volume_amount = volume_match.group(1)
                volume_unit = volume_match.group(2)
                if volume_unit == 'CL':
                    volume = f"{int(volume_amount) * 10}ML"
                else:
                    volume = f"{volume_amount}ML"
                return {'count': '', 'container': '', 'volume': volume}

        return {'count': '', 'container': '', 'volume': ''}

    def create_composite_sku_key(self, row: pd.Series, data_source: str) -> str:
        """
        Create a composite SKU key for matching purposes.
        Now focuses on brand/manufacturer/volume matching, ignoring pack count differences.

        Args:
            row (pd.Series): Data row from either A3 or Nielsen
            data_source (str): 'a3' or 'nielsen' to determine column mapping

        Returns:
            str: Composite SKU key for matching
        """
        if data_source == 'a3':
            manufacturer = self.normalize_manufacturer(row.get('Fabricant', ''))
            brand = self.normalize_brand(row.get('Marque', ''))
            sub_brand = self.normalize_brand(row.get('Marque fille', ''))
            pack_info = str(row.get('Conditionnement', '')).upper()

            # Extract pack components
            pack_components = self.extract_pack_components(pack_info, 'a3')

            # Create normalized pack info focusing on container and volume, not count
            normalized_pack = f"{pack_components['container']} {pack_components['volume']}".strip()

        elif data_source == 'nielsen':
            manufacturer = self.normalize_manufacturer(row.get('FABRICANT', ''))
            brand = self.normalize_brand(row.get('MARQUE', ''))
            sub_brand = self.normalize_brand(row.get('GAMME', ''))

            # Get Nielsen pack components
            container = str(row.get('CONDITIONNEMENT', '')).upper()
            count = str(row.get('NBR UNITE', '')).replace('X', '').strip()
            volume = str(row.get('CTN UNIT', '')).upper()

            # Normalize container names to match A3
            container_mapping = {
                'BOUTEILLE VERRE': 'BOUTEILLE',
                'CANETTE METAL': 'CANETTE',
                'BOITE': 'BOITE',
                'BOTTLE': 'BOUTEILLE',
                'CAN': 'CANETTE'
            }
            container = container_mapping.get(container, container)

            # Normalize volume
            volume_components = self.extract_pack_components(volume, 'nielsen')
            normalized_volume = volume_components['volume'] if volume_components['volume'] else volume

            # Create normalized pack info focusing on container and volume, not count
            normalized_pack = f"{container} {normalized_volume}".strip()

        else:
            raise ValueError("data_source must be 'a3' or 'nielsen'")

        # Create composite key without pack count to allow matching across different pack sizes
        key_parts = [manufacturer, brand, sub_brand, normalized_pack]
        composite_key = '|'.join([part.strip() for part in key_parts if part.strip()])

        return composite_key

    def perform_ean_matching(self) -> Dict[str, Dict]:
        """
        Perform EAN-based exact matching between A3 and Nielsen data.

        Returns:
            Dict[str, Dict]: Mapping results by year with EAN matches
        """
        logger.info("Starting EAN-based matching...")

        # Create EAN lookup from A3 data
        a3_ean_lookup = {}
        for idx, row in self.a3_data.iterrows():
            eans = self.extract_eans_from_a3(row['Ean'])
            for ean in eans:
                if ean not in a3_ean_lookup:
                    a3_ean_lookup[ean] = []
                a3_ean_lookup[ean].append({
                    'index': idx,
                    'row': row
                })

        logger.info(f"Created EAN lookup with {len(a3_ean_lookup)} unique EANs from A3 data")

        ean_matches = {}

        # Match against each Nielsen year
        for year, nielsen_df in self.nielsen_data_by_year.items():
            year_matches = []
            matched_pairs = set()  # Track (a3_index, nielsen_index) pairs to avoid duplicates

            for idx, nielsen_row in nielsen_df.iterrows():
                nielsen_upc = str(nielsen_row.get('UPC', '')).strip()

                # Clean Nielsen UPC (remove non-digits)
                clean_upc = re.sub(r'[^\d]', '', nielsen_upc)

                if clean_upc and clean_upc in a3_ean_lookup:
                    # Found EAN match! Select the best A3 match for this Nielsen record
                    best_a3_match = None
                    best_score = 0

                    for a3_match in a3_ean_lookup[clean_upc]:
                        # Check if this pair already exists
                        pair = (a3_match['index'], idx)
                        if pair in matched_pairs:
                            continue

                        # Calculate match quality score (prefer exact brand/manufacturer matches)
                        score = 100  # Base EAN match score

                        # Bonus for manufacturer match
                        a3_manufacturer = self.normalize_manufacturer(a3_match['row'].get('Fabricant', ''))
                        nielsen_manufacturer = self.normalize_manufacturer(nielsen_row.get('FABRICANT', ''))
                        if a3_manufacturer == nielsen_manufacturer:
                            score += 10

                        # Bonus for brand match
                        a3_brand = self.normalize_brand(a3_match['row'].get('Marque', ''))
                        nielsen_brand = self.normalize_brand(nielsen_row.get('MARQUE', ''))
                        if a3_brand == nielsen_brand:
                            score += 5

                        if score > best_score:
                            best_score = score
                            best_a3_match = a3_match

                    # Add the best match if found
                    if best_a3_match:
                        pair = (best_a3_match['index'], idx)
                        matched_pairs.add(pair)

                        match_record = {
                            'a3_index': best_a3_match['index'],
                            'nielsen_index': idx,
                            'a3_row': best_a3_match['row'],
                            'nielsen_row': nielsen_row,
                            'match_type': 'EAN_EXACT',
                            'confidence_score': best_score,
                            'matching_ean': clean_upc,
                            'match_notes': f'Exact EAN match: {clean_upc} (score: {best_score})'
                        }
                        year_matches.append(match_record)

            ean_matches[year] = year_matches
            logger.info(f"Found {len(year_matches)} EAN matches for year {year}")

        return ean_matches

    def perform_composite_sku_matching(self, ean_matches: Dict[str, Dict]) -> Dict[str, Dict]:
        """
        Perform composite SKU matching for items not matched by EAN.

        Args:
            ean_matches (Dict[str, Dict]): Results from EAN matching

        Returns:
            Dict[str, Dict]: Combined EAN and composite SKU matches
        """
        logger.info("Starting composite SKU matching...")

        # Get A3 indices that were already matched by EAN
        ean_matched_a3_indices = set()
        for year_matches in ean_matches.values():
            for match in year_matches:
                ean_matched_a3_indices.add(match['a3_index'])

        # Create composite SKU lookup for unmatched A3 items
        a3_composite_lookup = {}
        for idx, row in self.a3_data.iterrows():
            if idx not in ean_matched_a3_indices:  # Only unmatched items
                composite_key = self.create_composite_sku_key(row, 'a3')
                if composite_key not in a3_composite_lookup:
                    a3_composite_lookup[composite_key] = []
                a3_composite_lookup[composite_key].append({
                    'index': idx,
                    'row': row
                })

        logger.info(f"Created composite SKU lookup with {len(a3_composite_lookup)} keys for unmatched A3 items")

        # Combine EAN matches with composite matches
        all_matches = {}

        for year, nielsen_df in self.nielsen_data_by_year.items():
            # Start with EAN matches for this year
            year_matches = ean_matches.get(year, []).copy()

            # Get Nielsen indices already matched by EAN
            ean_matched_nielsen_indices = set()
            matched_pairs = set()  # Track (a3_index, nielsen_index) pairs

            for match in year_matches:
                ean_matched_nielsen_indices.add(match['nielsen_index'])
                matched_pairs.add((match['a3_index'], match['nielsen_index']))

            # Track which A3 indices have been used in composite matches
            used_a3_indices = set()

            # Try composite matching for unmatched Nielsen items
            for idx, nielsen_row in nielsen_df.iterrows():
                if idx not in ean_matched_nielsen_indices:  # Only unmatched items
                    composite_key = self.create_composite_sku_key(nielsen_row, 'nielsen')

                    if composite_key in a3_composite_lookup:
                        # Found composite match! Select the best A3 match for this Nielsen record
                        best_a3_match = None
                        best_score = 0
                        best_pack_size_note = ""

                        for a3_match in a3_composite_lookup[composite_key]:
                            # Check if this A3 index has already been used
                            if a3_match['index'] in used_a3_indices:
                                continue

                            # Check if this pair already exists
                            pair = (a3_match['index'], idx)
                            if pair in matched_pairs:
                                continue

                            # Calculate match quality score
                            score = 95  # Base composite match score

                            # Extract pack components for detailed comparison
                            a3_pack_components = self.extract_pack_components(
                                a3_match['row'].get('Conditionnement', ''), 'a3'
                            )
                            nielsen_volume = str(nielsen_row.get('CTN UNIT', '')).upper()
                            nielsen_count = str(nielsen_row.get('NBR UNITE', '')).replace('X', '').strip()

                            # Bonus for exact volume match
                            if a3_pack_components['volume'] and nielsen_volume:
                                nielsen_volume_normalized = self.extract_pack_components(nielsen_volume, 'nielsen')['volume']
                                if nielsen_volume_normalized and a3_pack_components['volume'] == nielsen_volume_normalized:
                                    score += 10
                                elif nielsen_volume in a3_pack_components['volume']:
                                    score += 5

                            # Bonus for container type match
                            a3_container = a3_pack_components['container']
                            nielsen_container = str(nielsen_row.get('CONDITIONNEMENT', '')).upper()
                            container_mapping = {
                                'BOUTEILLE VERRE': 'BOUTEILLE',
                                'CANETTE METAL': 'CANETTE',
                                'BOITE': 'BOITE'
                            }
                            nielsen_container_normalized = container_mapping.get(nielsen_container, nielsen_container)
                            if a3_container and a3_container == nielsen_container_normalized:
                                score += 5

                            # Small penalty for pack count mismatch (but don't exclude the match)
                            if a3_pack_components['count'] and nielsen_count:
                                try:
                                    a3_count = int(a3_pack_components['count'])
                                    nielsen_count_int = int(nielsen_count)
                                    if a3_count != nielsen_count_int:
                                        score -= 2  # Small penalty, but still allow the match
                                        # Add note about pack size difference
                                        pack_size_note = f" (Pack sizes differ: A3={a3_count}, Nielsen={nielsen_count_int})"
                                    else:
                                        score += 3  # Bonus for exact pack count match
                                        pack_size_note = ""
                                except (ValueError, TypeError):
                                    pack_size_note = ""
                            else:
                                pack_size_note = ""

                            if score > best_score:
                                best_score = score
                                best_a3_match = a3_match
                                best_pack_size_note = pack_size_note

                        # Add the best match if found
                        if best_a3_match:
                            pair = (best_a3_match['index'], idx)
                            matched_pairs.add(pair)
                            used_a3_indices.add(best_a3_match['index'])

                            match_record = {
                                'a3_index': best_a3_match['index'],
                                'nielsen_index': idx,
                                'a3_row': best_a3_match['row'],
                                'nielsen_row': nielsen_row,
                                'match_type': 'COMPOSITE_SKU',
                                'confidence_score': best_score,
                                'matching_key': composite_key,
                                'match_notes': f'Composite SKU match: {composite_key} (score: {best_score}){best_pack_size_note}'
                            }
                            year_matches.append(match_record)

            all_matches[year] = year_matches
            composite_count = len(year_matches) - len(ean_matches.get(year, []))
            logger.info(f"Found {composite_count} additional composite matches for year {year}")

        return all_matches

    def generate_mapping_output(self, all_matches: Dict[str, Dict]) -> None:
        """
        Generate Excel output with mapping results.

        Args:
            all_matches (Dict[str, Dict]): All matching results by year
        """
        logger.info("Generating mapping output...")

        output_file = Path("SKU_Mapping_A3_Nielsen_2025.xlsx")

        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:

            # Summary sheet
            summary_data = []
            total_ean_matches = 0
            total_composite_matches = 0
            total_matches = 0

            for year, matches in all_matches.items():
                ean_count = sum(1 for m in matches if m['match_type'] == 'EAN_EXACT')
                composite_count = sum(1 for m in matches if m['match_type'] == 'COMPOSITE_SKU')
                year_total = len(matches)

                total_ean_matches += ean_count
                total_composite_matches += composite_count
                total_matches += year_total

                summary_data.append({
                    'Year': year,
                    'EAN_Matches': ean_count,
                    'Composite_Matches': composite_count,
                    'Total_Matches': year_total,
                    'Nielsen_Records': len(self.nielsen_data_by_year[year])
                })

            # Add total row
            summary_data.append({
                'Year': 'TOTAL',
                'EAN_Matches': total_ean_matches,
                'Composite_Matches': total_composite_matches,
                'Total_Matches': total_matches,
                'Nielsen_Records': sum(len(df) for df in self.nielsen_data_by_year.values())
            })

            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Mapping_Summary', index=False)

            # Year-wise mapping sheets
            for year, matches in all_matches.items():
                if not matches:
                    continue

                mapping_data = []
                for match in matches:
                    a3_row = match['a3_row']
                    nielsen_row = match['nielsen_row']

                    mapping_data.append({
                        'A3_SKU_Identifier': a3_row.get('SKU_', ''),
                        'A3_Manufacturer': a3_row.get('Fabricant', ''),
                        'A3_Brand': a3_row.get('Marque', ''),
                        'A3_Sub_Brand': a3_row.get('Marque fille', ''),
                        'A3_Pack_Format': a3_row.get('Conditionnement', ''),
                        'A3_EAN_Codes': a3_row.get('Ean', ''),
                        'Nielsen_UPC': nielsen_row.get('UPC', ''),
                        'Nielsen_Manufacturer': nielsen_row.get('FABRICANT', ''),
                        'Nielsen_Brand': nielsen_row.get('MARQUE', ''),
                        'Nielsen_Sub_Brand': nielsen_row.get('GAMME', ''),
                        'Nielsen_Container': nielsen_row.get('CONDITIONNEMENT', ''),
                        'Nielsen_Count': nielsen_row.get('NBR UNITE', ''),
                        'Nielsen_Volume': nielsen_row.get('CTN UNIT', ''),
                        'Match_Type': match['match_type'],
                        'Confidence_Score': match['confidence_score'],
                        'Match_Logic': match['match_notes']
                    })

                mapping_df = pd.DataFrame(mapping_data)
                sheet_name = f'Mapping_{year}'
                mapping_df.to_excel(writer, sheet_name=sheet_name, index=False)

                logger.info(f"Generated {len(mapping_data)} mappings for year {year}")

        logger.info(f"Mapping output saved to: {output_file}")

        # Print summary statistics
        print("\n" + "="*60)
        print("SKU MAPPING SUMMARY")
        print("="*60)
        print(f"Total A3 Records: {len(self.a3_data)}")
        print(f"Total Nielsen Records: {sum(len(df) for df in self.nielsen_data_by_year.values())}")
        print(f"Total EAN Matches: {total_ean_matches}")
        print(f"Total Composite Matches: {total_composite_matches}")
        print(f"Total Successful Matches: {total_matches}")
        print(f"Overall Match Rate: {(total_matches/len(self.a3_data)*100):.1f}%")
        print("="*60)

    def run_mapping_process(self) -> None:
        """
        Execute the complete SKU mapping process.

        This is the main method that orchestrates the entire mapping workflow:
        1. Load data from both files
        2. Perform EAN matching
        3. Perform composite SKU matching
        4. Generate output files
        """
        try:
            logger.info("Starting SKU mapping process...")

            # Step 1: Load data
            self.load_data()

            # Step 2: EAN matching
            ean_matches = self.perform_ean_matching()

            # Step 3: Composite SKU matching
            all_matches = self.perform_composite_sku_matching(ean_matches)

            # Step 4: Generate output
            self.generate_mapping_output(all_matches)

            logger.info("SKU mapping process completed successfully!")

        except Exception as e:
            logger.error(f"Error in mapping process: {e}")
            raise


def main():
    """
    Main function to run the SKU mapping process.

    This function initializes the SkuMapper with the correct file paths
    and executes the complete mapping workflow.
    """
    # File paths - update these to match your actual file locations
    a3_file_path = "a3_distribution_march_2025_Latest.xlsx"
    nielsen_file_path = "CARREFOUR (incl. Drive) Data Pull 3.xlsx"

    # Initialize and run the mapper
    mapper = SkuMapper(a3_file_path, nielsen_file_path)
    mapper.run_mapping_process()


if __name__ == "__main__":
    main()
